#include "rtsp/connection_manager.hpp"
#include "rtsp/gstreamer_rtsp_client.hpp"
#include <iostream>
#include <sched.h>
#include <unistd.h>
#include <sys/syscall.h>
#include <pthread.h>
#include <cstdlib>
#include <thread>
#include <chrono>

namespace aibox {
namespace rtsp {

// ConnectionManager implementation
ConnectionManager::ConnectionManager(const RTSPConnectionConfig& config)
    : config_(config)
    , state_(ConnectionState::DISCONNECTED)
    , should_stop_(false)
    , mpp_status_(HardwareAccelStatus::DISABLED)
    , rga_status_(HardwareAccelStatus::DISABLED)
    , dmabuf_enabled_(false)
    , thermal_throttling_(false)
    , adaptive_quality_(false) {

    // Initialize components
    auth_handler_ = std::make_unique<AuthenticationHandler>(config_.username, config_.password);
    retry_manager_ = std::make_unique<RetryManager>(config_);

    // Create GStreamer RTSP client
    try {
        gstreamer_client_ = std::make_unique<GStreamerRTSPClient>(config_);

        // Set up callbacks
        gstreamer_client_->setStateChangeCallback([this](ConnectionState new_state) {
            handleStateChange(new_state);
        });

        gstreamer_client_->setErrorCallback([this](const StreamId& stream_id, ErrorCategory category, const std::string& message) {
            handleError(category, message);
        });

        gstreamer_client_->setBufferCallback([this](const std::vector<uint8_t>& buffer, Timestamp timestamp) {
            handleBufferReceived(buffer, timestamp);
        });

        gstreamer_client_->setNALUnitCallback([this](const NALUnit& nal_unit) {
            handleNALUnitReceived(nal_unit);
        });

        std::cout << "[ConnectionManager] GStreamer client created successfully" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "[ConnectionManager] Failed to create GStreamer client: " << e.what() << std::endl;
        throw;
    }

    // Apply RK3588 optimizations
    optimizeForRK3588();

    // Validate configuration
    if (!validateConfig()) {
        throw std::invalid_argument("Invalid RTSP connection configuration");
    }

    std::cout << "[ConnectionManager] Initialized for stream: " << config_.rtsp_url << std::endl;
}

ConnectionManager::~ConnectionManager() {
    disconnect();
    std::cout << "[ConnectionManager] Destroyed" << std::endl;
}

ConnectionResult ConnectionManager::connect() {
    std::lock_guard<std::mutex> lock(state_mutex_);

    if (state_ == ConnectionState::CONNECTED || state_ == ConnectionState::STREAMING) {
        return ConnectionResult(true);
    }

    std::cout << "[ConnectionManager] Connecting to: " << config_.rtsp_url << std::endl;

    // Check system resources
    if (!checkSystemResources()) {
        return ConnectionResult("Insufficient system resources", ErrorCategory::RESOURCE_ERROR);
    }

    // Check hardware availability
    if (!checkHardwareAvailability()) {
        std::cout << "[ConnectionManager] Hardware acceleration not available, using software fallback" << std::endl;
    }

    state_ = ConnectionState::CONNECTING;

    // Connect using GStreamer client
    if (!gstreamer_client_) {
        state_ = ConnectionState::ERROR;
        return ConnectionResult("GStreamer client not available", ErrorCategory::HARDWARE_ERROR);
    }

    // Initialize and connect GStreamer client
    if (!gstreamer_client_->initialize()) {
        state_ = ConnectionState::ERROR;
        return ConnectionResult("Failed to initialize GStreamer client: " + gstreamer_client_->getLastError(),
                               ErrorCategory::HARDWARE_ERROR);
    }

    if (!gstreamer_client_->connect()) {
        state_ = ConnectionState::ERROR;
        return ConnectionResult("Failed to connect GStreamer client: " + gstreamer_client_->getLastError(),
                               ErrorCategory::NETWORK_ERROR);
    }

    // Update hardware acceleration status from GStreamer client
    mpp_status_ = gstreamer_client_->getMPPStatus();
    rga_status_ = gstreamer_client_->getRGAStatus();
    dmabuf_enabled_ = gstreamer_client_->isDMABufEnabled();

    // Start worker threads for monitoring
    should_stop_ = false;
    monitoring_thread_ = std::make_unique<std::thread>(&ConnectionManager::monitoringWorker, this);

    state_ = ConnectionState::CONNECTED;
    statistics_.connection_start_time = std::chrono::steady_clock::now();

    std::cout << "[ConnectionManager] Successfully connected using GStreamer" << std::endl;
    return ConnectionResult(true);
}

void ConnectionManager::disconnect() {
    std::lock_guard<std::mutex> lock(state_mutex_);

    if (state_ == ConnectionState::DISCONNECTED) {
        return;
    }

    std::cout << "[ConnectionManager] Disconnecting..." << std::endl;

    should_stop_ = true;
    state_ = ConnectionState::DISCONNECTED;

    // Disconnect GStreamer client
    if (gstreamer_client_) {
        gstreamer_client_->disconnect();
    }

    // Stop worker threads
    if (monitoring_thread_ && monitoring_thread_->joinable()) {
        monitoring_thread_->join();
    }

    std::cout << "[ConnectionManager] Disconnected" << std::endl;
}

bool ConnectionManager::isConnected() const {
    ConnectionState current_state = state_.load();
    return current_state == ConnectionState::CONNECTED || current_state == ConnectionState::STREAMING;
}

ConnectionState ConnectionManager::getState() const {
    return state_.load();
}

void ConnectionManager::updateConfig(const RTSPConnectionConfig& new_config) {
    std::lock_guard<std::mutex> lock(state_mutex_);
    config_ = new_config;
    
    // Update sub-components
    if (auth_handler_) {
        auth_handler_->updateCredentials(config_.username, config_.password);
    }
    if (retry_manager_) {
        retry_manager_->updateConfig(config_);
    }
    
    std::cout << "[ConnectionManager] Configuration updated" << std::endl;
}

const RTSPConnectionConfig& ConnectionManager::getConfig() const {
    return config_;
}

StreamStatistics ConnectionManager::getStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    // Create a copy manually since atomic types can't be copied
    StreamStatistics copy;
    copy.packets_received = statistics_.packets_received.load();
    copy.packets_lost = statistics_.packets_lost.load();
    copy.bytes_received = statistics_.bytes_received.load();
    copy.reconnect_count = statistics_.reconnect_count.load();
    copy.current_fps = statistics_.current_fps.load();
    copy.current_bitrate_kbps = statistics_.current_bitrate_kbps.load();
    copy.average_latency_ms = statistics_.average_latency_ms.load();
    copy.mpp_decode_count = statistics_.mpp_decode_count.load();
    copy.rga_process_count = statistics_.rga_process_count.load();
    copy.software_fallback_count = statistics_.software_fallback_count.load();
    copy.memory_usage_bytes = statistics_.memory_usage_bytes.load();
    copy.cpu_usage_percent = statistics_.cpu_usage_percent.load();
    copy.network_errors = statistics_.network_errors.load();
    copy.decode_errors = statistics_.decode_errors.load();
    copy.hardware_errors = statistics_.hardware_errors.load();
    copy.last_packet_time = statistics_.last_packet_time;
    copy.connection_start_time = statistics_.connection_start_time;

    return copy;
}

ConnectionHealth ConnectionManager::getHealth() const {
    ConnectionHealth health;
    health.state = state_.load();
    health.last_activity = statistics_.last_packet_time;
    health.uptime = std::chrono::duration_cast<Duration>(
        std::chrono::steady_clock::now() - statistics_.connection_start_time);
    health.reconnect_count = statistics_.reconnect_count.load();
    // Calculate packet loss rate manually from atomic values
    uint64_t total_packets = statistics_.packets_received.load() + statistics_.packets_lost.load();
    double loss_rate = total_packets > 0 ? static_cast<double>(statistics_.packets_lost.load()) / total_packets : 0.0;
    health.packet_loss_rate_percent = static_cast<uint32_t>(loss_rate * 100);
    health.average_latency_ms = statistics_.average_latency_ms.load();
    health.hardware_acceleration_active = (mpp_status_ == HardwareAccelStatus::HARDWARE_ACTIVE);
    
    return health;
}

HardwareAccelStatus ConnectionManager::getMPPStatus() const {
    return mpp_status_;
}

HardwareAccelStatus ConnectionManager::getRGAStatus() const {
    return rga_status_;
}

void ConnectionManager::setDataCallback(StreamDataCallback callback) {
    data_callback_ = callback;
    std::cout << "[ConnectionManager] Data callback set" << std::endl;
}

void ConnectionManager::setErrorCallback(StreamErrorCallback callback) {
    error_callback_ = callback;
    std::cout << "[ConnectionManager] Error callback set" << std::endl;
}

void ConnectionManager::handleThermalThrottling(int temperature) {
    thermal_throttling_ = (temperature >= 80);

    if (thermal_throttling_) {
        // Reduce hardware acceleration to lower heat
        mpp_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
        rga_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
        std::cout << "[ConnectionManager] Thermal throttling activated at " << temperature << "°C" << std::endl;
    } else {
        // Restore hardware acceleration
        if (config_.use_mpp_decoder) {
            mpp_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
        }
        if (config_.use_rga_scaler) {
            rga_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
        }
        std::cout << "[ConnectionManager] Thermal throttling deactivated" << std::endl;
    }
}

void ConnectionManager::setAdaptiveQuality(bool enable) {
    // TODO: Implement adaptive quality setting
    std::cout << "[ConnectionManager] Adaptive quality " << (enable ? "enabled" : "disabled") << std::endl;
}

// Private methods (skeleton implementations)
void ConnectionManager::connectionWorker() {
    // Set CPU affinity for RK3588
    setCPUAffinity();
    
    std::cout << "[ConnectionManager] Connection worker started" << std::endl;
    
    while (!should_stop_) {
        // TODO: Implement GStreamer pipeline processing
        // This will handle:
        // - RTSP stream reception
        // - RTP packet processing
        // - NAL unit extraction
        // - Hardware decoder integration
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        // Update statistics
        updateStatistics();
    }
    
    std::cout << "[ConnectionManager] Connection worker stopped" << std::endl;
}

void ConnectionManager::monitoringWorker() {
    std::cout << "[ConnectionManager] Monitoring worker started" << std::endl;
    
    while (!should_stop_) {
        // Check connection health
        checkConnectionHealth();
        
        // Handle thermal management
        if (thermal_throttling_) {
            // TODO: Implement thermal throttling logic
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    std::cout << "[ConnectionManager] Monitoring worker stopped" << std::endl;
}

void ConnectionManager::optimizeForRK3588() {
    // Configure memory limits based on platform
    configureMemoryLimits();
    
    // Enable hardware acceleration by default
    mpp_status_ = HardwareAccelStatus::DISABLED;  // Will be enabled in configureHardwareAcceleration
    rga_status_ = HardwareAccelStatus::DISABLED;
    dmabuf_enabled_ = config_.use_dmabuf_zerocopy;
    
    std::cout << "[ConnectionManager] Applied RK3588 optimizations" << std::endl;
}

void ConnectionManager::setCPUAffinity() {
    // Set thread affinity to cores 2-3 (RK3588 little cores for RTSP)
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(2, &cpuset);  // Core 2
    CPU_SET(3, &cpuset);  // Core 3
    
    if (pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) != 0) {
        std::cerr << "[ConnectionManager] Failed to set CPU affinity" << std::endl;
    } else {
        std::cout << "[ConnectionManager] Set CPU affinity to cores 2-3" << std::endl;
    }
}

bool ConnectionManager::validateConfig() const {
    return config_.isValid() && 
           !config_.rtsp_url.empty() &&
           config_.timeout_ms > 0;
}

bool ConnectionManager::checkSystemResources() const {
    // TODO: Implement system resource checking
    // - Available memory
    // - CPU load
    // - Hardware accelerator availability
    return true;
}

bool ConnectionManager::checkHardwareAvailability() const {
    // TODO: Implement hardware availability checking
    // - MPP decoder availability
    // - RGA scaler availability
    // - DMABUF support
    return true;
}

bool ConnectionManager::initializeGStreamerPipeline() {
    // TODO: Implement GStreamer pipeline initialization
    // This will create the pipeline:
    // rtspsrc -> rtph264depay -> h264parse -> mppvideodec -> rgaconvert -> appsink
    std::cout << "[ConnectionManager] Initializing GStreamer pipeline (TODO)" << std::endl;

    // For now, simulate failure for test URLs
    if (config_.rtsp_url.find("test.example.com") != std::string::npos) {
        std::cout << "[ConnectionManager] Test URL detected, simulating connection failure" << std::endl;
        return false;
    }

    return true;
}

void ConnectionManager::cleanupGStreamerPipeline() {
    // TODO: Implement GStreamer pipeline cleanup
    std::cout << "[ConnectionManager] Cleaning up GStreamer pipeline (TODO)" << std::endl;
}

bool ConnectionManager::configureHardwareAcceleration() {
    // TODO: Implement hardware acceleration configuration
    if (config_.use_mpp_decoder) {
        mpp_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
        std::cout << "[ConnectionManager] MPP decoder enabled" << std::endl;
    }
    
    if (config_.use_rga_scaler) {
        rga_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
        std::cout << "[ConnectionManager] RGA scaler enabled" << std::endl;
    }
    
    return true;
}

bool ConnectionManager::authenticateConnection() {
    // TODO: Implement RTSP authentication
    if (auth_handler_ && auth_handler_->hasCredentials()) {
        std::cout << "[ConnectionManager] Authenticating with credentials" << std::endl;
        return auth_handler_->authenticate(AuthenticationHandler::AuthMethod::BASIC);
    }
    return true;
}

void ConnectionManager::updateStatistics() {
    // TODO: Implement statistics collection
    statistics_.last_packet_time = std::chrono::steady_clock::now();
}

void ConnectionManager::checkConnectionHealth() {
    // TODO: Implement connection health checking
}

void ConnectionManager::configureMemoryLimits() {
    // TODO: Implement memory limit configuration based on platform
    std::cout << "[ConnectionManager] Configured memory limits for RK3588" << std::endl;
}

// AuthenticationHandler implementation (skeleton)
AuthenticationHandler::AuthenticationHandler(const std::string& username, const std::string& password)
    : username_(username), password_(password) {
}

bool AuthenticationHandler::authenticate(AuthMethod method, const std::string& challenge) {
    // TODO: Implement authentication logic
    std::cout << "[AuthenticationHandler] Authenticating with method: " << static_cast<int>(method) << std::endl;
    return !username_.empty() && !password_.empty();
}

bool AuthenticationHandler::hasCredentials() const {
    return !username_.empty() && !password_.empty();
}

void AuthenticationHandler::updateCredentials(const std::string& username, const std::string& password) {
    username_ = username;
    password_ = password;
}

// RetryManager implementation (skeleton)
RetryManager::RetryManager(const RTSPConnectionConfig& config)
    : max_retry_count_(config.retry_count)
    , base_delay_ms_(config.retry_delay_ms)
    , max_delay_ms_(config.retry_delay_max_ms)
    , jitter_ms_(config.retry_jitter_ms)
    , attempt_count_(0)
    , success_count_(0)
    , failure_count_(0) {
}

bool RetryManager::shouldRetry(ErrorCategory category) const {
    return attempt_count_ < static_cast<uint32_t>(max_retry_count_) && 
           isRetriableError(category);
}

Duration RetryManager::getNextRetryDelay() {
    uint32_t attempt = attempt_count_++;
    Duration base_delay = calculateExponentialBackoff(attempt);
    return addJitter(base_delay);
}

void RetryManager::recordAttempt(bool success) {
    if (success) {
        success_count_++;
    } else {
        failure_count_++;
    }
}

void RetryManager::reset() {
    attempt_count_ = 0;
    success_count_ = 0;
    failure_count_ = 0;
}

void RetryManager::updateConfig(const RTSPConnectionConfig& config) {
    max_retry_count_ = config.retry_count;
    base_delay_ms_ = config.retry_delay_ms;
    max_delay_ms_ = config.retry_delay_max_ms;
    jitter_ms_ = config.retry_jitter_ms;
}

bool RetryManager::isRetriableError(ErrorCategory category) const {
    return category == ErrorCategory::NETWORK_ERROR || 
           category == ErrorCategory::PROTOCOL_ERROR;
}

Duration RetryManager::calculateExponentialBackoff(uint32_t attempt) const {
    int delay_ms = base_delay_ms_ * (1 << attempt);  // Exponential backoff
    delay_ms = std::min(delay_ms, max_delay_ms_);
    return Duration(delay_ms);
}

Duration RetryManager::addJitter(Duration base_delay) const {
    // Add random jitter to prevent thundering herd
    int jitter = (rand() % (2 * jitter_ms_)) - jitter_ms_;
    return base_delay + Duration(jitter);
}

// GStreamer callback handlers
void ConnectionManager::handleStateChange(ConnectionState new_state) {
    ConnectionState old_state = state_.exchange(new_state);

    if (old_state != new_state) {
        std::cout << "[ConnectionManager] State changed from " << static_cast<int>(old_state)
                  << " to " << static_cast<int>(new_state) << std::endl;

        // Update statistics based on state change
        if (new_state == ConnectionState::STREAMING) {
            statistics_.connection_start_time = std::chrono::steady_clock::now();
        } else if (new_state == ConnectionState::ERROR || new_state == ConnectionState::DISCONNECTED) {
            statistics_.network_errors++;
        }

        // Notify event callback
        if (event_callback_) {
            std::string event_msg = "State changed to " + std::to_string(static_cast<int>(new_state));
            event_callback_(config_.stream_id, event_msg);
        }
    }
}

void ConnectionManager::handleError(ErrorCategory category, const std::string& message) {
    std::cerr << "[ConnectionManager] Error (category " << static_cast<int>(category)
              << "): " << message << std::endl;

    // Update statistics
    switch (category) {
        case ErrorCategory::NETWORK_ERROR:
            statistics_.network_errors++;
            break;
        case ErrorCategory::HARDWARE_ERROR:
            statistics_.hardware_errors++;
            break;
        case ErrorCategory::PROTOCOL_ERROR:
            statistics_.network_errors++;  // Use network_errors for protocol errors
            break;
        case ErrorCategory::CODEC_ERROR:
            statistics_.decode_errors++;
            break;
        case ErrorCategory::RESOURCE_ERROR:
            // Handle resource errors (no specific counter)
            break;
        case ErrorCategory::CONFIGURATION_ERROR:
            // Handle configuration errors (no specific counter)
            break;
        case ErrorCategory::THERMAL_ERROR:
            // Handle thermal errors (no specific counter)
            break;
    }

    // Notify error callback
    if (error_callback_) {
        error_callback_(config_.stream_id, category, message);
    }
}

void ConnectionManager::handleBufferReceived(const std::vector<uint8_t>& buffer, Timestamp timestamp) {
    // Update statistics
    statistics_.packets_received++;
    statistics_.bytes_received += buffer.size();
    statistics_.last_packet_time = timestamp;

    // Forward to data callback if set
    if (data_callback_) {
        // Create a NAL unit from the buffer (simplified)
        NALUnit nal_unit;
        nal_unit.data = buffer;
        nal_unit.timestamp = timestamp;
        nal_unit.stream_id = config_.stream_id;
        nal_unit.type = NALUnitType::H264_NON_IDR;  // Default, should be detected properly

        data_callback_(config_.stream_id, nal_unit);
    }
}

void ConnectionManager::handleNALUnitReceived(const NALUnit& nal_unit) {
    // Update statistics based on NAL unit type
    if (nal_unit.is_keyframe) {
        // This is a keyframe, update FPS calculation
        static auto last_keyframe_time = std::chrono::steady_clock::now();
        auto current_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - last_keyframe_time);

        if (duration.count() > 0) {
            statistics_.current_fps = static_cast<uint32_t>(1000.0f / duration.count());
        }
        last_keyframe_time = current_time;
    }

    // Forward to data callback
    if (data_callback_) {
        data_callback_(config_.stream_id, nal_unit);
    }
}

} // namespace rtsp
} // namespace aibox
