#include <gtest/gtest.h>
#include "rtsp/thread_safe_queue.hpp"
#include "rtsp/rtsp_types.hpp"

#include <thread>
#include <vector>
#include <chrono>
#include <atomic>

using namespace aibox::rtsp;

class ThreadSafeQueueTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.max_size = 100;
        config_.enable_statistics = true;
        config_.use_lock_free = false;
    }
    
    QueueConfig config_;
};

// Basic functionality tests
TEST_F(ThreadSafeQueueTest, BasicEnqueueDequeue) {
    ThreadSafeQueue<int> queue(config_);
    
    EXPECT_TRUE(queue.empty());
    EXPECT_EQ(queue.size(), 0);
    
    // Test enqueue
    EXPECT_TRUE(queue.enqueue(42));
    EXPECT_FALSE(queue.empty());
    EXPECT_EQ(queue.size(), 1);
    
    // Test dequeue
    int value;
    EXPECT_TRUE(queue.dequeue(value));
    EXPECT_EQ(value, 42);
    EXPECT_TRUE(queue.empty());
    EXPECT_EQ(queue.size(), 0);
}

TEST_F(ThreadSafeQueueTest, TryOperations) {
    ThreadSafeQueue<int> queue(config_);
    
    // Try enqueue
    EXPECT_TRUE(queue.tryEnqueue(10));
    EXPECT_TRUE(queue.tryEnqueue(20));
    
    // Try dequeue
    int value;
    EXPECT_TRUE(queue.tryDequeue(value));
    EXPECT_EQ(value, 10);
    EXPECT_TRUE(queue.tryDequeue(value));
    EXPECT_EQ(value, 20);
    
    // Try dequeue on empty queue
    EXPECT_FALSE(queue.tryDequeue(value));
}

TEST_F(ThreadSafeQueueTest, MoveSemantics) {
    // Test move semantics with a movable type (vector)
    ThreadSafeQueue<std::vector<int>> queue(config_);

    std::vector<int> data = {1, 2, 3, 4, 5};
    size_t original_size = data.size();

    EXPECT_TRUE(queue.enqueue(std::move(data)));
    EXPECT_TRUE(data.empty()); // Moved from

    std::vector<int> result;
    EXPECT_TRUE(queue.dequeue(result));
    EXPECT_EQ(result.size(), original_size);
    EXPECT_EQ(result[0], 1);
    EXPECT_EQ(result[4], 5);
}

TEST_F(ThreadSafeQueueTest, CapacityLimits) {
    config_.max_size = 3;
    ThreadSafeQueue<int> queue(config_);
    
    // Fill to capacity
    EXPECT_TRUE(queue.tryEnqueue(1));
    EXPECT_TRUE(queue.tryEnqueue(2));
    EXPECT_TRUE(queue.tryEnqueue(3));
    EXPECT_TRUE(queue.full());
    
    // Should fail when full
    EXPECT_FALSE(queue.tryEnqueue(4));
    
    // Dequeue one and try again
    int value;
    EXPECT_TRUE(queue.tryDequeue(value));
    EXPECT_FALSE(queue.full());
    EXPECT_TRUE(queue.tryEnqueue(4));
}

TEST_F(ThreadSafeQueueTest, TimeoutOperations) {
    config_.max_size = 1;
    ThreadSafeQueue<int> queue(config_);
    
    // Fill queue
    EXPECT_TRUE(queue.enqueue(1));
    
    // Timeout on enqueue
    auto start = std::chrono::steady_clock::now();
    EXPECT_FALSE(queue.enqueue(2, 100)); // 100ms timeout
    auto elapsed = std::chrono::steady_clock::now() - start;
    EXPECT_GE(elapsed, std::chrono::milliseconds(90));
    
    // Clear queue
    int value;
    EXPECT_TRUE(queue.dequeue(value));
    
    // Timeout on dequeue
    start = std::chrono::steady_clock::now();
    EXPECT_FALSE(queue.dequeue(value, 100)); // 100ms timeout
    elapsed = std::chrono::steady_clock::now() - start;
    EXPECT_GE(elapsed, std::chrono::milliseconds(90));
}

// Lock-free mode tests
TEST_F(ThreadSafeQueueTest, LockFreeMode) {
    config_.use_lock_free = true;
    ThreadSafeQueue<int> queue(config_);
    
    EXPECT_TRUE(queue.isLockFreeMode());
    
    // Basic operations should work
    EXPECT_TRUE(queue.enqueue(42));
    int value;
    EXPECT_TRUE(queue.dequeue(value));
    EXPECT_EQ(value, 42);
}

TEST_F(ThreadSafeQueueTest, SwitchLockFreeMode) {
    ThreadSafeQueue<int> queue(config_);
    
    EXPECT_FALSE(queue.isLockFreeMode());
    
    // Switch to lock-free
    queue.setLockFreeMode(true);
    EXPECT_TRUE(queue.isLockFreeMode());
    
    // Test operations
    EXPECT_TRUE(queue.enqueue(123));
    int value;
    EXPECT_TRUE(queue.dequeue(value));
    EXPECT_EQ(value, 123);
    
    // Switch back
    queue.setLockFreeMode(false);
    EXPECT_FALSE(queue.isLockFreeMode());
}

// Concurrent access tests
TEST_F(ThreadSafeQueueTest, ConcurrentProducerConsumer) {
    ThreadSafeQueue<int> queue(config_);
    std::atomic<int> produced(0);
    std::atomic<int> consumed(0);
    const int num_items = 1000;
    
    // Producer thread
    std::thread producer([&queue, &produced, num_items]() {
        for (int i = 0; i < num_items; ++i) {
            while (!queue.enqueue(i, 10)) {
                std::this_thread::yield();
            }
            produced++;
        }
    });
    
    // Consumer thread
    std::thread consumer([&queue, &consumed, num_items]() {
        int value;
        while (consumed < num_items) {
            if (queue.dequeue(value, 10)) {
                consumed++;
            }
        }
    });
    
    producer.join();
    consumer.join();
    
    EXPECT_EQ(produced.load(), num_items);
    EXPECT_EQ(consumed.load(), num_items);
    EXPECT_TRUE(queue.empty());
}

TEST_F(ThreadSafeQueueTest, MultipleProducersConsumers) {
    ThreadSafeQueue<int> queue(config_);
    std::atomic<int> total_produced(0);
    std::atomic<int> total_consumed(0);
    const int num_threads = 4;
    const int items_per_thread = 250;
    
    std::vector<std::thread> producers;
    std::vector<std::thread> consumers;
    
    // Start producers
    for (int t = 0; t < num_threads; ++t) {
        producers.emplace_back([&queue, &total_produced, items_per_thread, t]() {
            for (int i = 0; i < items_per_thread; ++i) {
                int value = t * items_per_thread + i;
                while (!queue.enqueue(value, 10)) {
                    std::this_thread::yield();
                }
                total_produced++;
            }
        });
    }
    
    // Start consumers
    for (int t = 0; t < num_threads; ++t) {
        consumers.emplace_back([&queue, &total_consumed, items_per_thread]() {
            int value;
            for (int i = 0; i < items_per_thread; ++i) {
                while (!queue.dequeue(value, 10)) {
                    std::this_thread::yield();
                }
                total_consumed++;
            }
        });
    }
    
    // Wait for completion
    for (auto& t : producers) t.join();
    for (auto& t : consumers) t.join();
    
    EXPECT_EQ(total_produced.load(), num_threads * items_per_thread);
    EXPECT_EQ(total_consumed.load(), num_threads * items_per_thread);
    EXPECT_TRUE(queue.empty());
}

// Statistics tests
TEST_F(ThreadSafeQueueTest, Statistics) {
    ThreadSafeQueue<int> queue(config_);
    
    // Initial statistics
    auto stats = queue.getStatistics();
    EXPECT_EQ(stats.enqueue_count, 0);
    EXPECT_EQ(stats.dequeue_count, 0);
    EXPECT_EQ(stats.current_size, 0);
    
    // Perform operations
    queue.enqueue(1);
    queue.enqueue(2);
    int value;
    queue.dequeue(value);
    
    stats = queue.getStatistics();
    EXPECT_EQ(stats.enqueue_count, 2);
    EXPECT_EQ(stats.dequeue_count, 1);
    EXPECT_EQ(stats.current_size, 1);
    EXPECT_EQ(stats.peak_size, 2);
    
    // Reset statistics
    queue.resetStatistics();
    stats = queue.getStatistics();
    EXPECT_EQ(stats.enqueue_count, 0);
    EXPECT_EQ(stats.dequeue_count, 0);
}

// Back pressure tests
TEST_F(ThreadSafeQueueTest, BackPressure) {
    config_.max_size = 10;
    config_.enable_back_pressure = true;
    config_.back_pressure_threshold = 8;
    ThreadSafeQueue<int> queue(config_);
    
    EXPECT_FALSE(queue.isBackPressureActive());
    
    // Fill beyond threshold
    for (int i = 0; i < 9; ++i) {
        queue.enqueue(i);
    }
    
    EXPECT_TRUE(queue.isBackPressureActive());
    
    // Drain below threshold
    int value;
    for (int i = 0; i < 5; ++i) {
        queue.dequeue(value);
    }
    
    EXPECT_FALSE(queue.isBackPressureActive());
}

// Adaptive sizing tests
TEST_F(ThreadSafeQueueTest, AdaptiveSizing) {
    config_.enable_adaptive_sizing = true;
    config_.min_size = 10;
    config_.max_size = 100;
    config_.growth_factor = 2;
    ThreadSafeQueue<int> queue(config_);
    
    EXPECT_EQ(queue.capacity(), config_.max_size);
    
    // Simulate high utilization
    for (int i = 0; i < 1500; ++i) { // Trigger statistics threshold
        queue.enqueue(i);
        int value;
        if (i % 10 == 0) queue.dequeue(value); // Keep some items
    }
    
    // Note: Adaptive sizing requires time-based conditions
    // This test mainly verifies the interface works
}

// Performance optimization tests
TEST_F(ThreadSafeQueueTest, OptimizeForLatency) {
    ThreadSafeQueue<int> queue(config_);
    
    queue.optimizeForLatency();
    EXPECT_TRUE(queue.isLockFreeMode());
}

TEST_F(ThreadSafeQueueTest, OptimizeForThroughput) {
    ThreadSafeQueue<int> queue(config_);
    
    queue.optimizeForThroughput();
    EXPECT_FALSE(queue.isLockFreeMode());
}

// Stop/resume functionality
TEST_F(ThreadSafeQueueTest, StopResume) {
    ThreadSafeQueue<int> queue(config_);
    
    queue.enqueue(42);
    queue.stop();
    
    // Operations should fail when stopped
    EXPECT_FALSE(queue.enqueue(43));
    
    int value;
    EXPECT_TRUE(queue.dequeue(value)); // Can still dequeue existing items
    EXPECT_EQ(value, 42);
    
    EXPECT_FALSE(queue.dequeue(value)); // But not when empty
    
    // Resume operations
    queue.resume();
    EXPECT_TRUE(queue.enqueue(44));
    EXPECT_TRUE(queue.dequeue(value));
    EXPECT_EQ(value, 44);
}
