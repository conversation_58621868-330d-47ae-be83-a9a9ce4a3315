#include <gtest/gtest.h>
#include "rtsp/error_handler.hpp"
#include "rtsp/logger.hpp"
#include <thread>
#include <chrono>

using namespace aibox::rtsp;

class ErrorHandlerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize logger for testing
        LoggerConfig logger_config;
        logger_config.output = LogOutput::CONSOLE;
        logger_config.min_level = LogLevel::DEBUG_LEVEL;
        logger_config.enable_async_logging = false; // Synchronous for testing
        logger_ = std::make_shared<Logger>(logger_config);
        
        // Initialize error handler with test configuration
        RecoveryConfig recovery_config;
        recovery_config.max_retry_attempts = 2;
        recovery_config.retry_delay = std::chrono::milliseconds(10);
        recovery_config.enable_automatic_recovery = true;
        recovery_config.enable_thermal_protection = false; // Disable for testing
        recovery_config.enable_resource_monitoring = false; // Disable for testing
        
        error_handler_ = std::make_unique<ErrorHandler>(logger_, recovery_config);
    }
    
    void TearDown() override {
        error_handler_.reset();
        logger_.reset();
    }
    
    std::shared_ptr<Logger> logger_;
    std::unique_ptr<ErrorHandler> error_handler_;
};

// Basic Error Handling Tests

TEST_F(ErrorHandlerTest, BasicErrorHandling) {
    // Test basic error handling
    RecoveryAction action = error_handler_->handleError(
        ErrorCategory::NETWORK_ERROR,
        ErrorSeverity::ERROR,
        "Connection timeout",
        "ConnectionManager",
        "stream_001"
    );
    
    EXPECT_EQ(action, RecoveryAction::RESET_CONNECTION);
    
    // Check statistics
    auto stats = error_handler_->getStatistics();
    EXPECT_EQ(stats.total_errors, 1);
    EXPECT_EQ(stats.network_errors, 1);
    EXPECT_EQ(stats.recoverable_errors, 1);
}

TEST_F(ErrorHandlerTest, ErrorCategorization) {
    // Test different error categories
    struct TestCase {
        ErrorCategory category;
        ErrorSeverity severity;
        RecoveryAction expected_action;
    };
    
    std::vector<TestCase> test_cases = {
        {ErrorCategory::NETWORK_ERROR, ErrorSeverity::ERROR, RecoveryAction::RESET_CONNECTION},
        {ErrorCategory::PROTOCOL_ERROR, ErrorSeverity::WARNING, RecoveryAction::RESTART_STREAM},
        {ErrorCategory::CODEC_ERROR, ErrorSeverity::ERROR, RecoveryAction::RESTART_STREAM},
        {ErrorCategory::HARDWARE_ERROR, ErrorSeverity::CRITICAL, RecoveryAction::FALLBACK_SOFTWARE},
        {ErrorCategory::RESOURCE_ERROR, ErrorSeverity::WARNING, RecoveryAction::REDUCE_QUALITY},
        {ErrorCategory::THERMAL_ERROR, ErrorSeverity::CRITICAL, RecoveryAction::THERMAL_THROTTLE},
        {ErrorCategory::CONFIGURATION_ERROR, ErrorSeverity::ERROR, RecoveryAction::NONE}
    };
    
    for (const auto& test_case : test_cases) {
        RecoveryAction action = error_handler_->handleError(
            test_case.category,
            test_case.severity,
            "Test error message",
            "TestComponent",
            "test_stream"
        );
        
        EXPECT_EQ(action, test_case.expected_action) 
            << "Failed for category: " << static_cast<int>(test_case.category);
    }
}

TEST_F(ErrorHandlerTest, ErrorStatistics) {
    // Generate various errors
    error_handler_->handleError(ErrorCategory::NETWORK_ERROR, ErrorSeverity::ERROR, 
                               "Network error 1", "Component1", "stream1");
    error_handler_->handleError(ErrorCategory::NETWORK_ERROR, ErrorSeverity::WARNING, 
                               "Network error 2", "Component1", "stream1");
    error_handler_->handleError(ErrorCategory::HARDWARE_ERROR, ErrorSeverity::CRITICAL, 
                               "Hardware error", "Component2", "stream2");
    error_handler_->handleError(ErrorCategory::CODEC_ERROR, ErrorSeverity::ERROR, 
                               "Codec error", "Component3", "stream3");
    
    auto stats = error_handler_->getStatistics();
    EXPECT_EQ(stats.total_errors, 4);
    EXPECT_EQ(stats.network_errors, 2);
    EXPECT_EQ(stats.hardware_errors, 1);
    EXPECT_EQ(stats.codec_errors, 1);
    EXPECT_EQ(stats.recoverable_errors, 4); // All should be recoverable by default
}

TEST_F(ErrorHandlerTest, RecoveryReporting) {
    // Test recovery success reporting
    error_handler_->reportRecoverySuccess("stream1", RecoveryAction::RESET_CONNECTION, 
                                         "Connection reset successful");
    
    auto stats = error_handler_->getStatistics();
    EXPECT_EQ(stats.successful_recoveries, 1);

    // Test recovery failure reporting
    error_handler_->reportRecoveryFailure("stream1", RecoveryAction::RESTART_STREAM,
                                         "Stream restart failed");

    // Statistics should not change for failure reporting
    stats = error_handler_->getStatistics();
    EXPECT_EQ(stats.successful_recoveries, 1);
}

TEST_F(ErrorHandlerTest, ConfigurationManagement) {
    // Test configuration updates
    RecoveryConfig new_config;
    new_config.max_retry_attempts = 5;
    new_config.retry_delay = std::chrono::milliseconds(100);
    new_config.enable_automatic_recovery = false;
    
    error_handler_->updateConfig(new_config);
    
    auto retrieved_config = error_handler_->getConfig();
    EXPECT_EQ(retrieved_config.max_retry_attempts, 5);
    EXPECT_EQ(retrieved_config.retry_delay, std::chrono::milliseconds(100));
    EXPECT_FALSE(retrieved_config.enable_automatic_recovery);
}

// Callback Tests

TEST_F(ErrorHandlerTest, ErrorCallback) {
    bool callback_called = false;
    ErrorInfo received_error;
    
    error_handler_->setErrorCallback([&](const ErrorInfo& error) {
        callback_called = true;
        received_error = error;
    });
    
    error_handler_->handleError(ErrorCategory::NETWORK_ERROR, ErrorSeverity::ERROR,
                               "Test error", "TestComponent", "test_stream");
    
    EXPECT_TRUE(callback_called);
    EXPECT_EQ(received_error.category, ErrorCategory::NETWORK_ERROR);
    EXPECT_EQ(received_error.severity, ErrorSeverity::ERROR);
    EXPECT_EQ(received_error.message, "Test error");
    EXPECT_EQ(received_error.component, "TestComponent");
    EXPECT_EQ(received_error.stream_id, "test_stream");
}

TEST_F(ErrorHandlerTest, RecoveryCallback) {
    bool recovery_callback_called = false;
    RecoveryAction received_action;
    
    error_handler_->setRecoveryCallback([&](const ErrorInfo& error, RecoveryAction action) {
        recovery_callback_called = true;
        received_action = action;
        return true; // Simulate successful recovery
    });
    
    error_handler_->handleError(ErrorCategory::NETWORK_ERROR, ErrorSeverity::ERROR,
                               "Test error", "TestComponent", "test_stream");
    
    EXPECT_TRUE(recovery_callback_called);
    EXPECT_EQ(received_action, RecoveryAction::RESET_CONNECTION);
    
    // Check that successful recovery was recorded
    auto stats = error_handler_->getStatistics();
    EXPECT_EQ(stats.recovery_attempts, 1);
    EXPECT_EQ(stats.successful_recoveries, 1);
}

TEST_F(ErrorHandlerTest, MetricsCallback) {
    bool metrics_callback_called = false;
    ErrorStatistics received_stats;
    
    error_handler_->setMetricsCallback([&](const ErrorStatistics& stats) {
        metrics_callback_called = true;
        received_stats = stats;
    });
    
    // Generate some errors to trigger metrics
    error_handler_->handleError(ErrorCategory::NETWORK_ERROR, ErrorSeverity::ERROR,
                               "Test error", "TestComponent", "test_stream");
    
    // Note: Metrics callback is typically called by monitoring thread
    // For this test, we just verify the callback can be set
    EXPECT_TRUE(error_handler_->getStatistics().total_errors > 0);
}

// Performance and Health Tests

TEST_F(ErrorHandlerTest, ErrorRateCalculation) {
    auto start_time = std::chrono::steady_clock::now();
    
    // Generate multiple errors quickly
    for (int i = 0; i < 10; ++i) {
        error_handler_->handleError(ErrorCategory::NETWORK_ERROR, ErrorSeverity::ERROR,
                                   "Error " + std::to_string(i), "TestComponent", "test_stream");
    }
    
    // Wait a bit to ensure time has passed
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    double error_rate = error_handler_->getErrorRate();
    EXPECT_GT(error_rate, 0.0);
    EXPECT_LT(error_rate, 1000.0); // Should be reasonable
}

TEST_F(ErrorHandlerTest, RecoverySuccessRate) {
    // Initially should be 100% (no attempts yet)
    EXPECT_DOUBLE_EQ(error_handler_->getRecoverySuccessRate(), 1.0);
    
    // Report some recovery attempts
    error_handler_->reportRecoverySuccess("stream1", RecoveryAction::RETRY, "Success");
    error_handler_->reportRecoverySuccess("stream2", RecoveryAction::RETRY, "Success");
    error_handler_->reportRecoveryFailure("stream3", RecoveryAction::RETRY, "Failed");
    
    // Manually update statistics for this test
    auto stats = error_handler_->getStatistics();
    // Note: In real implementation, recovery attempts would be incremented automatically
    
    double success_rate = error_handler_->getRecoverySuccessRate();
    EXPECT_GE(success_rate, 0.0);
    EXPECT_LE(success_rate, 1.0);
}

TEST_F(ErrorHandlerTest, SystemHealthCheck) {
    // Initially system should be healthy
    EXPECT_TRUE(error_handler_->isSystemHealthy());
    
    // Generate many errors to make system unhealthy
    for (int i = 0; i < 100; ++i) {
        error_handler_->handleError(ErrorCategory::NETWORK_ERROR, ErrorSeverity::CRITICAL,
                                   "Critical error " + std::to_string(i), "TestComponent", "test_stream");
    }
    
    // System might become unhealthy due to high error rate
    // Note: This depends on the specific health check implementation
    bool is_healthy = error_handler_->isSystemHealthy();
    // We can't guarantee the result without knowing the exact thresholds
    EXPECT_TRUE(is_healthy || !is_healthy); // Just verify the method works
}

// Edge Cases and Error Conditions

TEST_F(ErrorHandlerTest, EmptyStreamId) {
    // Test handling errors without stream ID
    RecoveryAction action = error_handler_->handleError(
        ErrorCategory::CONFIGURATION_ERROR,
        ErrorSeverity::WARNING,
        "Configuration warning",
        "ConfigManager"
        // No stream_id provided
    );
    
    EXPECT_EQ(action, RecoveryAction::NONE);
    
    auto stats = error_handler_->getStatistics();
    EXPECT_EQ(stats.total_errors, 1);
    EXPECT_EQ(stats.configuration_errors, 1);
}

TEST_F(ErrorHandlerTest, ErrorInfoStructure) {
    ErrorInfo error_info;
    error_info.category = ErrorCategory::HARDWARE_ERROR;
    error_info.severity = ErrorSeverity::CRITICAL;
    error_info.message = "Hardware failure detected";
    error_info.component = "HardwareManager";
    error_info.stream_id = "critical_stream";
    error_info.context["temperature"] = "95C";
    error_info.context["component"] = "MPP_DECODER";
    error_info.suggested_action = RecoveryAction::FALLBACK_SOFTWARE;
    error_info.is_recoverable = true;
    error_info.error_code = 12345;
    
    RecoveryAction action = error_handler_->handleError(error_info);
    EXPECT_EQ(action, RecoveryAction::FALLBACK_SOFTWARE);
    
    auto stats = error_handler_->getStatistics();
    EXPECT_EQ(stats.hardware_errors, 1);
    EXPECT_EQ(stats.recoverable_errors, 1);
}

TEST_F(ErrorHandlerTest, StatisticsReset) {
    // Generate some errors
    error_handler_->handleError(ErrorCategory::NETWORK_ERROR, ErrorSeverity::ERROR,
                               "Error 1", "Component1", "stream1");
    error_handler_->handleError(ErrorCategory::CODEC_ERROR, ErrorSeverity::WARNING,
                               "Error 2", "Component2", "stream2");
    
    auto stats_before = error_handler_->getStatistics();
    EXPECT_GT(stats_before.total_errors, 0);

    // Reset statistics
    error_handler_->resetStatistics();

    auto stats_after = error_handler_->getStatistics();
    EXPECT_EQ(stats_after.total_errors, 0);
    EXPECT_EQ(stats_after.network_errors, 0);
    EXPECT_EQ(stats_after.codec_errors, 0);
}
