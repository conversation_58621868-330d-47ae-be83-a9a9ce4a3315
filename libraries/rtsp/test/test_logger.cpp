#include <gtest/gtest.h>
#include "rtsp/logger.hpp"
#include "rtsp/error_handler.hpp"
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>

using namespace aibox::rtsp;

class LoggerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for test logs
        test_log_dir_ = std::filesystem::temp_directory_path() / "rtsp_logger_test";
        std::filesystem::create_directories(test_log_dir_);
        
        test_log_file_ = test_log_dir_ / "test.log";
        
        // Basic logger configuration for testing
        config_.output = LogOutput::BOTH;
        config_.log_file_path = test_log_file_.string();
        config_.min_level = LogLevel::DEBUG_LEVEL;
        config_.enable_async_logging = false; // Synchronous for testing
        config_.enable_rotation = false; // Disable rotation for most tests
        config_.max_file_size_mb = 1; // Small size for rotation tests
        config_.enable_timestamps = true;
        config_.enable_thread_ids = false; // Disable for consistent testing
        
        logger_ = std::make_unique<Logger>(config_);
    }
    
    void TearDown() override {
        logger_.reset();
        
        // Clean up test files
        try {
            std::filesystem::remove_all(test_log_dir_);
        } catch (const std::exception&) {
            // Ignore cleanup errors
        }
    }
    
    std::string readLogFile() {
        std::ifstream file(test_log_file_);
        if (!file.is_open()) {
            return "";
        }
        
        std::stringstream buffer;
        buffer << file.rdbuf();
        return buffer.str();
    }
    
    LoggerConfig config_;
    std::unique_ptr<Logger> logger_;
    std::filesystem::path test_log_dir_;
    std::filesystem::path test_log_file_;
};

// Basic Logging Tests

TEST_F(LoggerTest, BasicLogging) {
    logger_->info("Test message", "TestComponent");
    logger_->flush();
    
    std::string log_content = readLogFile();
    EXPECT_TRUE(log_content.find("Test message") != std::string::npos);
    EXPECT_TRUE(log_content.find("[INFO]") != std::string::npos);
    EXPECT_TRUE(log_content.find("[TestComponent]") != std::string::npos);
}

TEST_F(LoggerTest, LogLevels) {
    // Test all log levels
    logger_->trace("Trace message", "TestComponent");
    logger_->debug("Debug message", "TestComponent");
    logger_->info("Info message", "TestComponent");
    logger_->warning("Warning message", "TestComponent");
    logger_->error("Error message", "TestComponent");
    logger_->critical("Critical message", "TestComponent");
    logger_->fatal("Fatal message", "TestComponent");
    logger_->flush();
    
    std::string log_content = readLogFile();
    
    // All levels should be present (min_level is DEBUG_LEVEL)
    EXPECT_TRUE(log_content.find("[TRACE]") != std::string::npos);
    EXPECT_TRUE(log_content.find("[DEBUG]") != std::string::npos);
    EXPECT_TRUE(log_content.find("[INFO]") != std::string::npos);
    EXPECT_TRUE(log_content.find("[WARNING]") != std::string::npos);
    EXPECT_TRUE(log_content.find("[ERROR]") != std::string::npos);
    EXPECT_TRUE(log_content.find("[CRITICAL]") != std::string::npos);
    EXPECT_TRUE(log_content.find("[FATAL]") != std::string::npos);
}

TEST_F(LoggerTest, LogLevelFiltering) {
    // Set minimum level to WARNING
    logger_->setLogLevel(LogLevel::WARNING);
    
    logger_->debug("Debug message", "TestComponent");
    logger_->info("Info message", "TestComponent");
    logger_->warning("Warning message", "TestComponent");
    logger_->error("Error message", "TestComponent");
    logger_->flush();
    
    std::string log_content = readLogFile();
    
    // Only WARNING and above should be present
    EXPECT_TRUE(log_content.find("[DEBUG]") == std::string::npos);
    EXPECT_TRUE(log_content.find("[INFO]") == std::string::npos);
    EXPECT_TRUE(log_content.find("[WARNING]") != std::string::npos);
    EXPECT_TRUE(log_content.find("[ERROR]") != std::string::npos);
}

TEST_F(LoggerTest, StreamIdLogging) {
    logger_->info("Stream message", "StreamComponent", "stream_123");
    logger_->flush();
    
    std::string log_content = readLogFile();
    EXPECT_TRUE(log_content.find("[stream_123]") != std::string::npos);
}

TEST_F(LoggerTest, ContextLogging) {
    std::unordered_map<std::string, std::string> context;
    context["key1"] = "value1";
    context["key2"] = "value2";
    
    logger_->log(LogLevel::INFO, "Context message", "TestComponent", "stream_001", context);
    logger_->flush();
    
    std::string log_content = readLogFile();
    EXPECT_TRUE(log_content.find("key1=value1") != std::string::npos);
    EXPECT_TRUE(log_content.find("key2=value2") != std::string::npos);
}

// Error Integration Tests

TEST_F(LoggerTest, ErrorInfoLogging) {
    ErrorInfo error_info;
    error_info.category = ErrorCategory::NETWORK_ERROR;
    error_info.severity = ErrorSeverity::ERROR;
    error_info.message = "Connection failed";
    error_info.component = "NetworkManager";
    error_info.stream_id = "stream_456";
    error_info.context["retry_count"] = "3";
    error_info.context["timeout"] = "5000ms";
    error_info.is_recoverable = true;
    error_info.error_code = 404;
    
    logger_->logError(error_info);
    logger_->flush();
    
    std::string log_content = readLogFile();
    EXPECT_TRUE(log_content.find("Connection failed") != std::string::npos);
    EXPECT_TRUE(log_content.find("[ERROR]") != std::string::npos);
    EXPECT_TRUE(log_content.find("[NetworkManager]") != std::string::npos);
    EXPECT_TRUE(log_content.find("[stream_456]") != std::string::npos);
    EXPECT_TRUE(log_content.find("retry_count=3") != std::string::npos);
    EXPECT_TRUE(log_content.find("error_code=404") != std::string::npos);
    EXPECT_TRUE(log_content.find("recoverable=true") != std::string::npos);
}

// Configuration Tests

TEST_F(LoggerTest, ConfigurationUpdate) {
    LoggerConfig new_config = config_;
    new_config.min_level = LogLevel::ERROR;
    new_config.enable_timestamps = false;
    
    logger_->updateConfig(new_config);
    
    auto retrieved_config = logger_->getConfig();
    EXPECT_EQ(retrieved_config.min_level, LogLevel::ERROR);
    EXPECT_FALSE(retrieved_config.enable_timestamps);
    
    // Test that the new configuration is applied
    logger_->info("Info message", "TestComponent"); // Should be filtered out
    logger_->error("Error message", "TestComponent"); // Should be logged
    logger_->flush();
    
    std::string log_content = readLogFile();
    EXPECT_TRUE(log_content.find("Info message") == std::string::npos);
    EXPECT_TRUE(log_content.find("Error message") != std::string::npos);
}

TEST_F(LoggerTest, ConsoleOnlyOutput) {
    LoggerConfig console_config = config_;
    console_config.output = LogOutput::CONSOLE;
    
    Logger console_logger(console_config);
    console_logger.info("Console only message", "TestComponent");
    console_logger.flush();
    
    // File should be empty or not exist
    std::string log_content = readLogFile();
    EXPECT_TRUE(log_content.empty() || log_content.find("Console only message") == std::string::npos);
}

// File Management Tests

TEST_F(LoggerTest, LogFileCreation) {
    // File should be created after first log
    EXPECT_FALSE(std::filesystem::exists(test_log_file_));
    
    logger_->info("First message", "TestComponent");
    logger_->flush();
    
    EXPECT_TRUE(std::filesystem::exists(test_log_file_));
    EXPECT_GT(logger_->getLogFileSize(), 0);
}

TEST_F(LoggerTest, LogRotation) {
    // Enable rotation with very small file size
    LoggerConfig rotation_config = config_;
    rotation_config.enable_rotation = true;
    rotation_config.max_file_size_mb = 0; // Very small to trigger rotation
    rotation_config.max_backup_files = 2;
    
    Logger rotation_logger(rotation_config);
    
    // Write enough data to trigger rotation
    for (int i = 0; i < 100; ++i) {
        rotation_logger.info("Log message " + std::to_string(i) + 
                           " with some additional content to increase file size", 
                           "TestComponent");
    }
    rotation_logger.flush();
    
    // Force rotation
    rotation_logger.rotateLog();
    
    // Check that backup files exist
    std::filesystem::path backup1 = test_log_file_.string() + ".1";
    // Note: Backup files might not exist if rotation logic differs
    // This test verifies the rotation method can be called without errors
    EXPECT_NO_THROW(rotation_logger.rotateLog());
}

// Statistics Tests

TEST_F(LoggerTest, LoggingStatistics) {
    auto initial_stats = logger_->getStatistics();
    EXPECT_EQ(initial_stats.total_entries, 0);
    
    // Log various levels
    logger_->debug("Debug", "Test");
    logger_->info("Info", "Test");
    logger_->warning("Warning", "Test");
    logger_->error("Error", "Test");
    logger_->critical("Critical", "Test");
    logger_->flush();
    
    auto stats = logger_->getStatistics();
    EXPECT_EQ(stats.total_entries, 5);
    EXPECT_EQ(stats.debug_entries, 1);
    EXPECT_EQ(stats.info_entries, 1);
    EXPECT_EQ(stats.warning_entries, 1);
    EXPECT_EQ(stats.error_entries, 1);
    EXPECT_EQ(stats.critical_entries, 1);
    EXPECT_GT(stats.bytes_written, 0);
}

TEST_F(LoggerTest, StatisticsReset) {
    // Generate some log entries
    logger_->info("Message 1", "Test");
    logger_->error("Message 2", "Test");
    logger_->flush();
    
    auto stats_before = logger_->getStatistics();
    EXPECT_GT(stats_before.total_entries, 0);

    // Reset statistics
    logger_->resetStatistics();

    auto stats_after = logger_->getStatistics();
    EXPECT_EQ(stats_after.total_entries, 0);
    EXPECT_EQ(stats_after.info_entries, 0);
    EXPECT_EQ(stats_after.error_entries, 0);
}

// Utility Function Tests

TEST_F(LoggerTest, LevelStringConversion) {
    EXPECT_EQ(Logger::levelToString(LogLevel::DEBUG_LEVEL), "DEBUG");
    EXPECT_EQ(Logger::levelToString(LogLevel::INFO), "INFO");
    EXPECT_EQ(Logger::levelToString(LogLevel::WARNING), "WARNING");
    EXPECT_EQ(Logger::levelToString(LogLevel::ERROR), "ERROR");
    EXPECT_EQ(Logger::levelToString(LogLevel::CRITICAL), "CRITICAL");
    EXPECT_EQ(Logger::levelToString(LogLevel::FATAL), "FATAL");

    EXPECT_EQ(Logger::stringToLevel("DEBUG"), LogLevel::DEBUG_LEVEL);
    EXPECT_EQ(Logger::stringToLevel("info"), LogLevel::INFO); // Case insensitive
    EXPECT_EQ(Logger::stringToLevel("WARNING"), LogLevel::WARNING);
    EXPECT_EQ(Logger::stringToLevel("invalid"), LogLevel::INFO); // Default
}

TEST_F(LoggerTest, TimestampGeneration) {
    std::string timestamp = Logger::getCurrentTimestamp();
    EXPECT_FALSE(timestamp.empty());
    EXPECT_TRUE(timestamp.find(":") != std::string::npos); // Should contain time separators
    
    // Test custom format
    std::string custom_timestamp = Logger::getCurrentTimestamp("%Y-%m-%d");
    EXPECT_TRUE(custom_timestamp.find("-") != std::string::npos); // Should contain date separators
}

TEST_F(LoggerTest, LevelEnabledCheck) {
    logger_->setLogLevel(LogLevel::WARNING);
    
    EXPECT_FALSE(logger_->isLevelEnabled(LogLevel::TRACE));
    EXPECT_FALSE(logger_->isLevelEnabled(LogLevel::DEBUG_LEVEL));
    EXPECT_FALSE(logger_->isLevelEnabled(LogLevel::INFO));
    EXPECT_TRUE(logger_->isLevelEnabled(LogLevel::WARNING));
    EXPECT_TRUE(logger_->isLevelEnabled(LogLevel::ERROR));
    EXPECT_TRUE(logger_->isLevelEnabled(LogLevel::CRITICAL));
    EXPECT_TRUE(logger_->isLevelEnabled(LogLevel::FATAL));
}

// Global Logger Tests

TEST_F(LoggerTest, GlobalLoggerInitialization) {
    // Test global logger initialization
    LoggerConfig global_config;
    global_config.output = LogOutput::CONSOLE;
    global_config.min_level = LogLevel::INFO;
    
    initializeLogger(global_config);
    
    auto global_logger = getLogger();
    EXPECT_NE(global_logger, nullptr);
    EXPECT_EQ(global_logger->getLogLevel(), LogLevel::INFO);
    
    // Test that subsequent calls return the same instance
    auto global_logger2 = getLogger();
    EXPECT_EQ(global_logger, global_logger2);
}

// Async Logging Tests (if enabled)

TEST_F(LoggerTest, AsyncLogging) {
    LoggerConfig async_config = config_;
    async_config.enable_async_logging = true;
    async_config.async_queue_size = 100;
    
    Logger async_logger(async_config);
    
    // Log multiple messages quickly
    for (int i = 0; i < 10; ++i) {
        async_logger.info("Async message " + std::to_string(i), "AsyncTest");
    }
    
    // Flush to ensure all messages are written
    async_logger.flush();
    
    // Verify messages were logged (this test mainly ensures no crashes)
    auto stats = async_logger.getStatistics();
    EXPECT_EQ(stats.info_entries, 10);
}
