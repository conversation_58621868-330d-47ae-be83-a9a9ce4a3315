#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QFrame>
#include <QPixmap>
#include <QTimer>
#include <QPainter>
#include <QDateTime>
#include <QFont>
#include <QMouseEvent>
#include <QMenu>
#include <QAction>

/**
 * @brief Face detection overlay for the stream
 */
struct FaceOverlay {
    QRect boundingBox;
    QString name;
    QString confidence;
    bool isRecognized;
    QDateTime timestamp;
    
    FaceOverlay(const QRect& box = QRect(), const QString& n = "", const QString& conf = "", bool recognized = false)
        : boundingBox(box), name(n), confidence(conf), isRecognized(recognized), timestamp(QDateTime::currentDateTime()) {}
};

/**
 * @brief Single stream display widget with face recognition overlays
 * 
 * Displays a single RTSP stream with face detection overlays and stream information,
 * designed to match the provided UI mockup.
 */
class SingleStreamDisplay : public QFrame
{

public:
    explicit SingleStreamDisplay(QWidget* parent = nullptr);
    ~SingleStreamDisplay();

    // Stream control
    void setStreamUrl(const QString& url);
    void setStreamTitle(const QString& title);
    void startStream();
    void stopStream();
    bool isStreaming() const { return m_isStreaming; }

    // Face detection
    void addFaceOverlay(const FaceOverlay& face);
    void clearFaceOverlays();
    void setShowFaceOverlays(bool show);

    // Display settings
    void setShowStreamInfo(bool show);
    void setShowTimestamp(bool show);

protected:
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseDoubleClickEvent(QMouseEvent* event) override;
    void contextMenuEvent(QContextMenuEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;

private:
    void onUpdateFrame();
    void onCleanupOldFaces();
    void onToggleStream();
    void onShowStreamInfo();
    void setupUI();
    void setupContextMenu();
    void generateMockFrame();
    void generateMockFaces();
    void drawVideoFrame(QPainter& painter);
    void drawFaceOverlays(QPainter& painter);
    void drawStreamInfo(QPainter& painter);
    void drawTimestamp(QPainter& painter);
    void updateStreamInfo();

    // Stream properties
    QString m_streamUrl;
    QString m_streamTitle;
    bool m_isStreaming;
    
    // Display settings
    bool m_showFaceOverlays;
    bool m_showStreamInfo;
    bool m_showTimestamp;
    
    // Video display
    QPixmap m_currentFrame;
    int m_mockFrameCounter;
    QColor m_mockBackgroundColor;
    
    // Face detection
    QList<FaceOverlay> m_faceOverlays;
    
    // UI components
    QVBoxLayout* m_layout;
    QLabel* m_streamInfoLabel;
    
    // Timers
    QTimer* m_frameUpdateTimer;
    QTimer* m_faceCleanupTimer;
    
    // Context menu
    QMenu* m_contextMenu;
    QAction* m_toggleAction;
    QAction* m_infoAction;
    QAction* m_fullscreenAction;
    
    // Constants
    static const int FRAME_UPDATE_INTERVAL = 33; // ~30 FPS
    static const int FACE_CLEANUP_INTERVAL = 5000; // 5 seconds
    static const int FACE_TIMEOUT_MS = 10000; // 10 seconds
};
