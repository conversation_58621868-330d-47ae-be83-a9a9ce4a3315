#pragma once

#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QLabel>
#include <QPushButton>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QTimer>
#include <QSettings>

// Forward declarations
class SingleStreamDisplay;
class FaceRecognitionSidebar;
struct FaceResult;

/**
 * @brief Main window with single stream display and face recognition sidebar
 * 
 * This window provides a focused view of a single RTSP stream with face recognition
 * results displayed in a sidebar, matching the provided UI design.
 */
class SingleStreamMainWindow : public QMainWindow
{

public:
    explicit SingleStreamMainWindow(QWidget *parent = nullptr);
    ~SingleStreamMainWindow();

private:
    void onStreamClicked();
    void onStreamDoubleClicked();
    void onFaceResultClicked(const FaceResult& result);
    void onToggleFullscreen();
    void onShowSettings();
    void onAbout();
    void onUpdateMockData();
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void loadStyleSheet();
    void connectSignals();
    void startMockDataTimer();
    void addMockFaceResults();

    // UI Components
    QWidget* m_centralWidget;
    QHBoxLayout* m_mainLayout;
    QSplitter* m_mainSplitter;
    
    // Main display area
    SingleStreamDisplay* m_streamDisplay;
    
    // Sidebar
    FaceRecognitionSidebar* m_faceRecognitionSidebar;
    
    // Menu and toolbar actions
    QAction* m_fullscreenAction;
    QAction* m_settingsAction;
    QAction* m_aboutAction;
    QAction* m_exitAction;
    
    // Status bar elements
    QLabel* m_statusLabel;
    QLabel* m_streamStatusLabel;
    QLabel* m_faceCountLabel;
    
    // Timers
    QTimer* m_mockDataTimer;
    
    // Settings
    QSettings* m_settings;
    bool m_isFullscreen;
};
