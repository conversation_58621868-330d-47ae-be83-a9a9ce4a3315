#include "widgets/face_recognition_sidebar.hpp"
#include <QApplication>
#include <QPainter>
#include <QPainterPath>
#include <QFontMetrics>
#include <QDebug>

// FaceResultWidget implementation
FaceResultWidget::FaceResultWidget(const FaceResult& result, QWidget* parent)
    : QWidget(parent), m_result(result)
{
    setFixedHeight(80);
    setMinimumWidth(300);
    updateScaledAvatar();
}

void FaceResultWidget::updateResult(const FaceResult& result)
{
    m_result = result;
    updateScaledAvatar();
    update();
}

void FaceResultWidget::updateScaledAvatar()
{
    if (!m_result.avatar.isNull()) {
        m_scaledAvatar = m_result.avatar.scaled(60, 60, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    } else {
        // Generate default avatar
        m_scaledAvatar = QPixmap(60, 60);
        m_scaledAvatar.fill(QColor(100, 150, 200));
        
        QPainter painter(&m_scaledAvatar);
        painter.setRenderHint(QPainter::Antialiasing);
        painter.setPen(Qt::white);
        painter.setFont(QFont("Arial", 20, QFont::Bold));
        
        QString initials = m_result.name.isEmpty() ? "?" : QString(m_result.name.at(0).toUpper());
        painter.drawText(m_scaledAvatar.rect(), Qt::AlignCenter, initials);
    }
}

void FaceResultWidget::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // Background with subtle gradient
    QColor bgColor = (m_result.status == "Cho phép") ? QColor(45, 65, 85) : QColor(85, 45, 45);
    painter.fillRect(rect(), bgColor);

    // Left border indicator
    QColor borderColor = (m_result.status == "Cho phép") ? QColor(0, 200, 100) : QColor(255, 100, 100);
    painter.fillRect(0, 0, 4, height(), borderColor);

    // Avatar with rounded corners
    painter.setRenderHint(QPainter::Antialiasing);
    QPainterPath avatarPath;
    avatarPath.addRoundedRect(12, 10, 60, 60, 30, 30);
    painter.setClipPath(avatarPath);
    painter.drawPixmap(12, 10, m_scaledAvatar);
    painter.setClipping(false);

    // Text content
    painter.setPen(Qt::white);

    // Name
    QFont nameFont("Segoe UI", 13, QFont::Bold);
    painter.setFont(nameFont);
    painter.drawText(85, 28, m_result.name.isEmpty() ? "Unknown" : m_result.name);

    // Confidence percentage
    QFont confidenceFont("Segoe UI", 11, QFont::Bold);
    painter.setFont(confidenceFont);
    painter.setPen(borderColor);
    painter.drawText(85, 48, m_result.confidence);

    // Timestamp
    QFont timeFont("Segoe UI", 10);
    painter.setFont(timeFont);
    painter.setPen(QColor(160, 160, 160));
    painter.drawText(85, 65, m_result.timestamp);

    // Status indicator (right side)
    painter.setPen(borderColor);
    QFont statusFont("Segoe UI", 10, QFont::Bold);
    painter.setFont(statusFont);
    QFontMetrics fm(statusFont);
    QString statusText = (m_result.status == "Cho phép") ? "Cho phép" : "Từ chối";
    int textWidth = fm.horizontalAdvance(statusText);
    painter.drawText(width() - textWidth - 15, 35, statusText);
}

// FaceRecognitionSidebar implementation
FaceRecognitionSidebar::FaceRecognitionSidebar(QWidget* parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_titleLabel(nullptr)
    , m_countLabel(nullptr)
    , m_resultsList(nullptr)
    , m_maxResults(DEFAULT_MAX_RESULTS)
    , m_showTimestamps(true)
    , m_showConfidence(true)
    , m_cleanupTimer(nullptr)
{
    setupUI();
    
    // Setup cleanup timer
    m_cleanupTimer = new QTimer(this);
    connect(m_cleanupTimer, &QTimer::timeout, [this]() { onCleanupOldResults(); });
    m_cleanupTimer->start(CLEANUP_INTERVAL_MS);
}

FaceRecognitionSidebar::~FaceRecognitionSidebar()
{
    if (m_cleanupTimer) {
        m_cleanupTimer->stop();
    }
}

void FaceRecognitionSidebar::setupUI()
{
    setFixedWidth(350);
    setStyleSheet(R"(
        FaceRecognitionSidebar {
            background-color: #1e2329;
            border-left: 2px solid #2c5aa0;
        }
        QLabel {
            color: white;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        QListWidget {
            background-color: #1e2329;
            border: none;
            border-radius: 0px;
            outline: none;
        }
        QListWidget::item {
            border: none;
            padding: 0px;
            margin: 2px 0px;
        }
        QListWidget::item:selected {
            background-color: transparent;
        }
        QScrollBar:vertical {
            background-color: #2a3038;
            width: 8px;
            border-radius: 4px;
            margin: 0;
        }
        QScrollBar::handle:vertical {
            background-color: #4a5058;
            border-radius: 4px;
            min-height: 20px;
        }
        QScrollBar::handle:vertical:hover {
            background-color: #5a6068;
        }
        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {
            height: 0;
            width: 0;
        }
        QScrollBar::add-page:vertical,
        QScrollBar::sub-page:vertical {
            background: none;
        }
    )");
    
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(15, 15, 15, 15);
    m_mainLayout->setSpacing(10);
    
    // Title section with improved styling
    QHBoxLayout* titleLayout = new QHBoxLayout();

    m_titleLabel = new QLabel("DANH SÁCH NHẬN DIỆN");
    m_titleLabel->setStyleSheet(R"(
        font-size: 14px;
        font-weight: bold;
        color: white;
        padding: 8px 0px;
        letter-spacing: 1px;
    )");
    titleLayout->addWidget(m_titleLabel);

    titleLayout->addStretch();

    m_countLabel = new QLabel("3 người");
    m_countLabel->setStyleSheet(R"(
        font-size: 12px;
        color: #888;
        padding: 8px 0px;
    )");
    titleLayout->addWidget(m_countLabel);

    m_mainLayout->addLayout(titleLayout);
    
    // Results list
    m_resultsList = new QListWidget();
    m_resultsList->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);
    m_resultsList->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    connect(m_resultsList, &QListWidget::itemClicked, [this](QListWidgetItem* item) { onFaceResultClicked(item); });
    m_mainLayout->addWidget(m_resultsList);
    
    // Add some mock data for demonstration
    addFaceResult(FaceResult("Nguyễn Thái Quốc Huy", "95.5%", "Cho phép"));
    addFaceResult(FaceResult("Trịnh Quốc Bảo", "90.8%", "Cho phép"));
    addFaceResult(FaceResult("Trần Thành Long", "89.2%", "Từ chối"));
}

void FaceRecognitionSidebar::addFaceResult(const FaceResult& result)
{
    // Add to internal list
    m_faceResults.prepend(result); // Add to beginning for newest first
    
    // Limit the number of results
    while (m_faceResults.size() > m_maxResults) {
        m_faceResults.removeLast();
    }
    
    // Create widget for the result
    FaceResultWidget* resultWidget = new FaceResultWidget(result);
    
    // Create list item
    QListWidgetItem* item = new QListWidgetItem();
    item->setSizeHint(resultWidget->sizeHint());
    
    // Insert at the beginning
    m_resultsList->insertItem(0, item);
    m_resultsList->setItemWidget(item, resultWidget);
    
    // Remove excess items from the list widget
    while (m_resultsList->count() > m_maxResults) {
        QListWidgetItem* lastItem = m_resultsList->takeItem(m_resultsList->count() - 1);
        delete lastItem;
    }
    
    updateResultCount();
}

void FaceRecognitionSidebar::clearFaceResults()
{
    m_faceResults.clear();
    m_resultsList->clear();
    updateResultCount();
}

void FaceRecognitionSidebar::setMaxResults(int maxResults)
{
    m_maxResults = maxResults;
    
    // Remove excess results
    while (m_faceResults.size() > m_maxResults) {
        m_faceResults.removeLast();
    }
    
    while (m_resultsList->count() > m_maxResults) {
        QListWidgetItem* lastItem = m_resultsList->takeItem(m_resultsList->count() - 1);
        delete lastItem;
    }
    
    updateResultCount();
}

void FaceRecognitionSidebar::setShowTimestamps(bool show)
{
    m_showTimestamps = show;
    // Update all existing widgets
    for (int i = 0; i < m_resultsList->count(); ++i) {
        QListWidgetItem* item = m_resultsList->item(i);
        FaceResultWidget* widget = dynamic_cast<FaceResultWidget*>(m_resultsList->itemWidget(item));
        if (widget) {
            widget->update();
        }
    }
}

void FaceRecognitionSidebar::setShowConfidence(bool show)
{
    m_showConfidence = show;
    // Update all existing widgets
    for (int i = 0; i < m_resultsList->count(); ++i) {
        QListWidgetItem* item = m_resultsList->item(i);
        FaceResultWidget* widget = dynamic_cast<FaceResultWidget*>(m_resultsList->itemWidget(item));
        if (widget) {
            widget->update();
        }
    }
}

void FaceRecognitionSidebar::onCleanupOldResults()
{
    QDateTime cutoffTime = QDateTime::currentDateTime().addMSecs(-RESULT_TIMEOUT_MS);
    
    // Remove old results from internal list
    auto it = m_faceResults.begin();
    while (it != m_faceResults.end()) {
        QDateTime resultTime = QDateTime::fromString(it->timestamp, "hh:mm:ss");
        if (resultTime < cutoffTime) {
            it = m_faceResults.erase(it);
        } else {
            ++it;
        }
    }
    
    updateResultCount();
}

void FaceRecognitionSidebar::onFaceResultClicked(QListWidgetItem* item)
{
    FaceResultWidget* widget = dynamic_cast<FaceResultWidget*>(m_resultsList->itemWidget(item));
    if (widget) {
        // Face result clicked - could call a callback function here if needed
        qDebug() << "Face result clicked:" << widget->getResult().name;
    }
}

void FaceRecognitionSidebar::updateResultCount()
{
    m_countLabel->setText(QString("%1 người").arg(m_faceResults.size()));
}

QPixmap FaceRecognitionSidebar::generateDefaultAvatar(const QString& name)
{
    QPixmap avatar(60, 60);
    avatar.fill(QColor(100, 150, 200));
    
    QPainter painter(&avatar);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 20, QFont::Bold));
    
    QString initials = name.isEmpty() ? "?" : QString(name.at(0).toUpper());
    painter.drawText(avatar.rect(), Qt::AlignCenter, initials);
    
    return avatar;
}
