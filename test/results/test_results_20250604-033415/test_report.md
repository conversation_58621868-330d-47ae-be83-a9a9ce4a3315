# Stream Multiplexer Hardware Test Report

## Test Environment
- **Platform**: RK3588 OPi 5 Plus
- **Date**: Wed Jun  4 11:34:15 AM CST 2025
- **Kernel**: 5.10.160-rockchip-rk3588
- **Memory**: 7.8Gi
- **CPU Cores**: 8

## Test Configuration
- **Max Concurrent Streams**: 6 (4GB configuration)
- **Memory Limit**: 1200MB
- **Worker Threads**: 4
- **Test Duration**: 30 seconds

## Test Results
[0;34mStarting Stream Multiplexer hardware test...[0m
[0;34mChecking Orange Pi platform...[0m
[1;33m⚠ Not running on Orange Pi hardware[0m
[0;34mStarting Stream Multiplexer hardware test...[0m
[0;34mChecking Orange Pi platform...[0m
[1;33m⚠ Not running on Orange Pi hardware[0m
[0;34mStarting Stream Multiplexer hardware test...[0m
[0;34mChecking Orange Pi platform...[0m
[0;32m✓ Running on Orange Pi: RK3588 OPi 5 Plus[0m
[0;34mChecking RK3588 hardware capabilities...[0m
[1;33m⚠ MPP decoder service not found[0m
[0;32m✓ RGA hardware accelerator available[0m
[0;32m✓ Total memory: 7940MB[0m
[0;32m✓ CPU cores: 8[0m
[0;32m✓ Thermal zones: 7[0m
[0;34mTesting RTSP camera connectivity...[0m
[0;32m✓ RTSP camera reachable at ***************:554[0m
[0;34mCreating Stream Multiplexer test program...[0m
[0;32m✓ Test program created[0m
[0;34mMonitoring system resources during test...[0m
[0;32m✓ Resource monitoring started (PID: 185276)[0m
[0;34mCompiling and running Stream Multiplexer test...[0m
[0;32m✓ Test program compiled successfully[0m
[0;34mRunning Stream Multiplexer test...[0m
[0;32m✓ Stream Multiplexer test completed successfully[0m
[0;32m✓ All tests passed![0m
[0;34mGenerating test report...[0m
[0;32m✓ Test report generated: /tmp/stream_multiplexer_test/test_report.md[0m
[0;34m=== Test Summary ===[0m
Result: PASSED
Log file: /tmp/stream_multiplexer_test/test_results.log
Report: /tmp/stream_multiplexer_test/test_report.md
Test directory: /tmp/stream_multiplexer_test
[0;34mStarting Stream Multiplexer hardware test...[0m
[0;34mChecking Orange Pi platform...[0m
[0;32m✓ Running on Orange Pi: RK3588 OPi 5 Plus[0m
[0;34mChecking RK3588 hardware capabilities...[0m
[0;32m✓ MPP decoder service available at /dev/mpp_service[0m
[0;32m✓ MPP library version: 1.3.8[0m
[0;32m✓ MPP device accessible by current user[0m
[0;32m✓ RGA hardware accelerator available[0m
[0;32m✓ Total memory: 7940MB[0m
[0;32m✓ CPU cores: 8[0m
[0;32m✓ Thermal zones: 7[0m
[0;34mTesting RTSP camera connectivity...[0m
[0;32m✓ RTSP camera reachable at ***************:554[0m
[0;34mCreating Stream Multiplexer test program...[0m
[0;32m✓ Test program created[0m
[0;34mMonitoring system resources during test...[0m
[0;32m✓ Resource monitoring started (PID: 191719)[0m
[0;34mCompiling and running Stream Multiplexer test...[0m
[0;32m✓ Test program compiled successfully[0m
[0;34mRunning Stream Multiplexer test...[0m
[0;32m✓ Stream Multiplexer test completed successfully[0m
[0;32m✓ All tests passed![0m
[0;34mGenerating test report...[0m

## Resource Usage
11:04:41 - CPU: 7.1%, Memory: 12.9%, Temp: 38°C
11:04:42 - CPU: 18.5%, Memory: 12.9%, Temp: 39°C
11:04:43 - CPU: 3.1%, Memory: 12.8%, Temp: 38°C
11:04:44 - CPU: 6.2%, Memory: 12.8%, Temp: 38°C
11:04:46 - CPU: 2.8%, Memory: 12.8%, Temp: 38°C
11:04:47 - CPU: 7.4%, Memory: 12.8%, Temp: 38°C
11:04:48 - CPU: 0.0%, Memory: 12.8%, Temp: 38°C
11:04:49 - CPU: 3.3%, Memory: 12.8%, Temp: 38°C
11:04:51 - CPU: 0.0%, Memory: 12.8%, Temp: 38°C
11:04:52 - CPU: 2.9%, Memory: 12.8%, Temp: 38°C
11:04:53 - CPU: 0.0%, Memory: 12.8%, Temp: 38°C
11:04:54 - CPU: 3.1%, Memory: 12.8%, Temp: 38°C
11:04:56 - CPU: 3.0%, Memory: 12.8%, Temp: 38°C
11:04:57 - CPU: 6.7%, Memory: 12.8%, Temp: 38°C
11:04:58 - CPU: 7.7%, Memory: 12.8%, Temp: 38°C
11:04:59 - CPU: 4.8%, Memory: 12.8%, Temp: 38°C
11:05:01 - CPU: 0.0%, Memory: 12.8%, Temp: 38°C
11:05:02 - CPU: 5.9%, Memory: 12.8%, Temp: 38°C
11:05:03 - CPU: 2.9%, Memory: 12.8%, Temp: 38°C
11:05:04 - CPU: 0.0%, Memory: 12.8%, Temp: 38°C
11:05:06 - CPU: 3.3%, Memory: 12.8%, Temp: 38°C
11:05:07 - CPU: 3.0%, Memory: 12.8%, Temp: 38°C
11:05:08 - CPU: 3.3%, Memory: 12.8%, Temp: 38°C
11:05:09 - CPU: 0.0%, Memory: 12.8%, Temp: 38°C
11:05:11 - CPU: 3.1%, Memory: 12.8%, Temp: 38°C
11:05:12 - CPU: 7.4%, Memory: 12.8%, Temp: 38°C
11:33:44 - CPU: 80.0%, Memory: 15.0%, Temp: 54°C
11:33:45 - CPU: 68.4%, Memory: 14.8%, Temp: 54°C
11:33:46 - CPU: 27.3%, Memory: 14.8%, Temp: 53°C
11:33:47 - CPU: 90.9%, Memory: 14.8%, Temp: 54°C
11:33:48 - CPU: 90.9%, Memory: 14.8%, Temp: 53°C
11:33:50 - CPU: 90.9%, Memory: 14.8%, Temp: 53°C
11:33:51 - CPU: 88.0%, Memory: 14.8%, Temp: 53°C
11:33:52 - CPU: 40.0%, Memory: 14.8%, Temp: 53°C
11:33:53 - CPU: 10.0%, Memory: 14.8%, Temp: 53°C
11:33:55 - CPU: 50.0%, Memory: 14.8%, Temp: 52°C
11:33:56 - CPU: 90.0%, Memory: 14.8%, Temp: 53°C
11:33:57 - CPU: 84.8%, Memory: 14.8%, Temp: 53°C
11:33:58 - CPU: 65.4%, Memory: 14.8%, Temp: 53°C
11:34:00 - CPU: 69.6%, Memory: 14.8%, Temp: 53°C
11:34:01 - CPU: 53.6%, Memory: 14.8%, Temp: 53°C
11:34:02 - CPU: 71.4%, Memory: 14.8%, Temp: 53°C
11:34:03 - CPU: 44.8%, Memory: 14.8%, Temp: 53°C
11:34:05 - CPU: 92.3%, Memory: 14.8%, Temp: 53°C
11:34:06 - CPU: 78.3%, Memory: 14.8%, Temp: 53°C
11:34:07 - CPU: 78.6%, Memory: 14.8%, Temp: 53°C
11:34:08 - CPU: 88.2%, Memory: 14.8%, Temp: 53°C
11:34:09 - CPU: 74.2%, Memory: 14.8%, Temp: 53°C
11:34:11 - CPU: 57.9%, Memory: 14.8%, Temp: 53°C
11:34:12 - CPU: 46.2%, Memory: 14.8%, Temp: 53°C
11:34:13 - CPU: 87.5%, Memory: 14.8%, Temp: 53°C
11:34:14 - CPU: 79.2%, Memory: 14.8%, Temp: 53°C

## Hardware Capabilities
- MPP Decoder: Not Available
- RGA Accelerator: Available
- Thermal Monitoring: Available

## Recommendations
- For 4GB Orange Pi: Limit to 6 concurrent streams
- For 8GB Orange Pi: Can handle up to 12 concurrent streams
- Enable thermal throttling for sustained operation
- Use hardware acceleration when available
