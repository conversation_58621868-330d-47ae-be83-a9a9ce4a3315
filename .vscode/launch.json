{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Client (GUI)",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/bin/client_app",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [
                {
                    "name": "DISPLAY",
                    "value": "${env:DISPLAY}"
                },
                {
                    "name": "QT_X11_NO_MITSHM",
                    "value": "1"
                },
                {
                    "name": "QT_QPA_PLATFORM",
                    "value": "xcb"
                },
                {
                    "name": "QTWEBENGINE_DISABLE_SANDBOX",
                    "value": "1"
                },
                {
                    "name": "LIBGL_ALWAYS_INDIRECT",
                    "value": "1"
                },
                {
                    "name": "QT_LOGGING_RULES",
                    "value": "qt.qpa.xcb.warning=false"
                }
            ],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Set Disassembly Flavor to Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "build",
            "miDebuggerPath": "/usr/bin/gdb",
            "sourceFileMap": {
                "/workspaces/c-aibox": "${workspaceFolder}"
            },
            "logging": {
                "engineLogging": false,
                "programOutput": true,
                "trace": false
            },
            "visualizerFile": "/root/.vscode-server/data/User/workspaceStorage/21f280c2d962f72408c2e590cc3d1eff/tonka3000.qtvsctools/qt.natvis.xml"
        },
        {
            "name": "Debug Client (Headless)",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/bin/client_app",
            "args": [
                "--headless"
            ],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [
                {
                    "name": "QT_QPA_PLATFORM",
                    "value": "offscreen"
                },
                {
                    "name": "QTWEBENGINE_DISABLE_SANDBOX",
                    "value": "1"
                },
                {
                    "name": "QT_LOGGING_RULES",
                    "value": "qt.qpa.xcb.warning=false"
                }
            ],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Set Disassembly Flavor to Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "build",
            "miDebuggerPath": "/usr/bin/gdb",
            "sourceFileMap": {
                "/workspaces/c-aibox": "${workspaceFolder}"
            },
            "logging": {
                "engineLogging": false,
                "programOutput": true,
                "trace": false
            },
            "visualizerFile": "/root/.vscode-server/data/User/workspaceStorage/21f280c2d962f72408c2e590cc3d1eff/tonka3000.qtvsctools/qt.natvis.xml"
        },
        {
            "name": "Debug Single Stream Client (GUI)",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/apps/client/single_stream_client",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [
                {
                    "name": "DISPLAY",
                    "value": "${env:DISPLAY}"
                },
                {
                    "name": "QT_X11_NO_MITSHM",
                    "value": "1"
                },
                {
                    "name": "QT_QPA_PLATFORM",
                    "value": "xcb"
                },
                {
                    "name": "QTWEBENGINE_DISABLE_SANDBOX",
                    "value": "1"
                },
                {
                    "name": "LIBGL_ALWAYS_INDIRECT",
                    "value": "1"
                },
                {
                    "name": "QT_LOGGING_RULES",
                    "value": "qt.qpa.xcb.warning=false"
                }
            ],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Set Disassembly Flavor to Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "build",
            "miDebuggerPath": "/usr/bin/gdb",
            "sourceFileMap": {
                "/workspaces/c-aibox": "${workspaceFolder}"
            },
            "logging": {
                "engineLogging": false,
                "programOutput": true,
                "trace": false
            },
            "visualizerFile": "/root/.vscode-server/data/User/workspaceStorage/21f280c2d962f72408c2e590cc3d1eff/tonka3000.qtvsctools/qt.natvis.xml"
        },
        {
            "name": "Debug Face Recognition Demo (GUI)",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/apps/client/face_recognition_demo",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [
                {
                    "name": "DISPLAY",
                    "value": "${env:DISPLAY}"
                },
                {
                    "name": "QT_X11_NO_MITSHM",
                    "value": "1"
                },
                {
                    "name": "QT_QPA_PLATFORM",
                    "value": "xcb"
                },
                {
                    "name": "QTWEBENGINE_DISABLE_SANDBOX",
                    "value": "1"
                },
                {
                    "name": "LIBGL_ALWAYS_INDIRECT",
                    "value": "1"
                },
                {
                    "name": "QT_LOGGING_RULES",
                    "value": "qt.qpa.xcb.warning=false"
                }
            ],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Set Disassembly Flavor to Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "build",
            "miDebuggerPath": "/usr/bin/gdb",
            "sourceFileMap": {
                "/workspaces/c-aibox": "${workspaceFolder}"
            },
            "logging": {
                "engineLogging": false,
                "programOutput": true,
                "trace": false
            },
            "visualizerFile": "/root/.vscode-server/data/User/workspaceStorage/21f280c2d962f72408c2e590cc3d1eff/tonka3000.qtvsctools/qt.natvis.xml"
        },
        {
            "name": "Debug Server",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/bin/server",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Set Disassembly Flavor to Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "build",
            "miDebuggerPath": "/usr/bin/gdb",
            "sourceFileMap": {
                "/workspaces/c-aibox": "${workspaceFolder}"
            },
            "logging": {
                "engineLogging": false,
                "programOutput": true,
                "trace": false
            },
            "visualizerFile": "/root/.vscode-server/data/User/workspaceStorage/21f280c2d962f72408c2e590cc3d1eff/tonka3000.qtvsctools/qt.natvis.xml"
        },
        {
            "name": "Attach to Client",
            "type": "cppdbg",
            "request": "attach",
            "program": "${workspaceFolder}/build/apps/client/client",
            "processId": "${command:pickProcess}",
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ],
            "miDebuggerPath": "/usr/bin/gdb",
            "visualizerFile": "/root/.vscode-server/data/User/workspaceStorage/21f280c2d962f72408c2e590cc3d1eff/tonka3000.qtvsctools/qt.natvis.xml"
        },
        {
            "name": "Attach to Server",
            "type": "cppdbg",
            "request": "attach",
            "program": "${workspaceFolder}/build/bin/server",
            "processId": "${command:pickProcess}",
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ],
            "miDebuggerPath": "/usr/bin/gdb",
            "visualizerFile": "/root/.vscode-server/data/User/workspaceStorage/21f280c2d962f72408c2e590cc3d1eff/tonka3000.qtvsctools/qt.natvis.xml"
        },
        {
            "name": "Debug Client (Memory Analysis)",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/bin/client_app",
            "args": [
                "--headless"
            ],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [
                {
                    "name": "QT_QPA_PLATFORM",
                    "value": "offscreen"
                },
                {
                    "name": "QTWEBENGINE_DISABLE_SANDBOX",
                    "value": "1"
                },
                {
                    "name": "MALLOC_CHECK_",
                    "value": "2"
                },
                {
                    "name": "G_SLICE",
                    "value": "always-malloc"
                },
                {
                    "name": "QT_LOGGING_RULES",
                    "value": "qt.qpa.xcb.warning=false"
                }
            ],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Set Disassembly Flavor to Intel",
                    "text": "-gdb-set disassembly-flavor intel",
                    "ignoreFailures": true
                },
                {
                    "description": "Enable memory debugging",
                    "text": "-gdb-set environment MALLOC_CHECK_ 2",
                    "ignoreFailures": true
                },
                {
                    "description": "Break on memory errors",
                    "text": "-gdb-set breakpoint pending on",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "build",
            "miDebuggerPath": "/usr/bin/gdb",
            "sourceFileMap": {
                "/workspaces/c-aibox": "${workspaceFolder}"
            },
            "logging": {
                "engineLogging": false,
                "programOutput": true,
                "trace": false
            },
            "visualizerFile": "/root/.vscode-server/data/User/workspaceStorage/21f280c2d962f72408c2e590cc3d1eff/tonka3000.qtvsctools/qt.natvis.xml"
        },
        {
            "name": "Debug Client (LLDB)",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/bin/client_app",
            "args": [
                "--headless"
            ],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [
                {
                    "name": "QT_QPA_PLATFORM",
                    "value": "offscreen"
                },
                {
                    "name": "QTWEBENGINE_DISABLE_SANDBOX",
                    "value": "1"
                },
                {
                    "name": "QT_LOGGING_RULES",
                    "value": "qt.qpa.xcb.warning=false"
                }
            ],
            "externalConsole": false,
            "MIMode": "lldb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for lldb",
                    "text": "settings set target.x86-disassembly-flavor intel",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "build",
            "miDebuggerPath": "/usr/bin/lldb",
            "sourceFileMap": {
                "/workspaces/c-aibox": "${workspaceFolder}"
            },
            "logging": {
                "engineLogging": false,
                "programOutput": true,
                "trace": false
            },
            "visualizerFile": "/root/.vscode-server/data/User/workspaceStorage/21f280c2d962f72408c2e590cc3d1eff/tonka3000.qtvsctools/qt.natvis.xml"
        },
        {
            "name": "Debug Client (Core Dump)",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/bin/client_app",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [
                {
                    "name": "QT_QPA_PLATFORM",
                    "value": "offscreen"
                },
                {
                    "name": "QTWEBENGINE_DISABLE_SANDBOX",
                    "value": "1"
                }
            ],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Generate core dump on crash",
                    "text": "-gdb-set environment SEGFAULT_SIGNALS all",
                    "ignoreFailures": true
                },
                {
                    "description": "Set core dump size unlimited",
                    "text": "shell ulimit -c unlimited",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "build",
            "miDebuggerPath": "/usr/bin/gdb",
            "sourceFileMap": {
                "/workspaces/c-aibox": "${workspaceFolder}"
            },
            "logging": {
                "engineLogging": true,
                "programOutput": true,
                "trace": true
            },
            "visualizerFile": "/root/.vscode-server/data/User/workspaceStorage/21f280c2d962f72408c2e590cc3d1eff/tonka3000.qtvsctools/qt.natvis.xml"
        }
    ],
    "compounds": [
        {
            "name": "Debug Client & Server (GUI)",
            "configurations": [
                "Debug Client (GUI)",
                "Debug Server"
            ],
            "stopAll": true,
            "presentation": {
                "hidden": false,
                "group": "compound",
                "order": 1
            }
        },
        {
            "name": "Debug Client & Server (Headless)",
            "configurations": [
                "Debug Client (Headless)",
                "Debug Server"
            ],
            "stopAll": true,
            "presentation": {
                "hidden": false,
                "group": "compound",
                "order": 2
            }
        },
        {
            "name": "Debug Single Stream Client & Server",
            "configurations": [
                "Debug Single Stream Client (GUI)",
                "Debug Server"
            ],
            "stopAll": true,
            "presentation": {
                "hidden": false,
                "group": "compound",
                "order": 3
            }
        }
    ]
}